# Query VUP User Data Refactored - Test Documentation

This directory contains comprehensive mock tests for the `vups_server.query.query_vup_user_data_refactored` module.

## Test Structure

### Test File: `test_query_vup_user_data_refactored.py`

The test file is organized into the following test classes:

#### 1. `TestUserStatisticsFunctions`
Tests for functions that use `user_stats_service`:
- `query_current_stat_by_mid`
- `query_peroid_user_all_stat_by_uid_and_time`
- `query_whole_user_all_stat_by_uid_and_recent`
- `query_whole_user_follower_num_by_mid_and_recent`
- `query_now_user_follower_num_by_mid`
- `query_whole_dahanghai_num_by_mid_and_recent`
- `query_now_user_dahanghai_num_by_mid`
- `calculate_dahanghai_rate_by_mid`
- `calculate_follower_rate_by_mid`
- `query_current_follower_change_num`
- `query_current_dahanghai_change_num`

#### 2. `TestContentQueryFunctions`
Tests for functions that use `content_service`:
- `query_user_info_by_mid`
- `query_user_dynamics_by_mid`
- `query_all_video_list_by_mid`
- `query_current_videos`
- `query_current_dynamics`
- `query_recent_top_n_videos`
- `query_comments_for_wordcloud`
- `query_all_video_comments_by_mid`
- `query_all_dynamics_comments_by_mid`

#### 3. `TestAnalyticsFunctions`
Tests for functions that use `analytics_service`:
- `query_top_n_comments`
- `query_top_n_comments_user`
- `query_top_n_videos`
- `query_top_n_dynamics`
- `query_recent_relationships`
- `query_tieba_summaries_from_ai_gen_table`
- `query_rise_reason_from_ai_gen_table`
- `query_latest_fans_medal_rank`
- `query_fans_medal_rank_by_datetime`
- `query_video_ai_conclusion_by_bvid`
- `query_recent_comments_sentiment_value`

#### 4. `TestSpecializedFunctions`
Tests for functions that use `specialized_service`:
- `query_tieba_whole`
- `query_tieba_threads`
- `query_latest_dahanghai_list_by_uid`
- `query_dahanghai_list_by_uid_and_datetime`
- `query_followers_list`
- `query_followers_review_list`
- `query_followers_review_rate`
- `query_comment_wordcloud`
- `cleanup_old_wordcloud_files`
- `query_recent_info`
- `query_recent_info_with_view`

#### 5. `TestUtilityFunctions`
Tests for utility functions:
- `get_service_instances`

#### 6. `TestErrorHandling`
Tests for error handling scenarios:
- Service exception propagation
- Different types of exceptions from each service

#### 7. `TestParameterValidation`
Tests for parameter validation and edge cases:
- Empty parameters
- Zero/negative limits
- Boundary conditions

## Mock Strategy

### Service Mocking
- All four service instances are mocked using `@patch` decorators
- Services are mocked at the module level to intercept imports
- `AsyncMock` is used for async service methods

### Mock Data Patterns
- **asyncpg.Record**: Mocked using `MagicMock` with dict-like access
- **Statistics Data**: Lists of lists `[["date", value1, value2], ...]`
- **Content Data**: Lists of dictionaries with realistic field structures
- **User Info**: Dictionary structures matching expected database schemas
- **Counts/Rates**: Integer and float values with realistic ranges

### Test Scenarios
Each function is tested with:
1. **Success Cases**: Valid inputs with expected returns
2. **Edge Cases**: Empty results, None returns, boundary values
3. **Error Cases**: Service exceptions and error propagation
4. **Parameter Validation**: Different parameter combinations

## Running the Tests

### Prerequisites
Install required testing dependencies:
```bash
pip install pytest pytest-asyncio pytest-cov
```

### Run All Tests
```bash
# Using the test runner script
python tests/run_query_tests.py

# Or directly with pytest
pytest tests/test_query_vup_user_data_refactored.py -v
```

### Run Specific Test Classes
```bash
# Using the test runner script
python tests/run_query_tests.py TestUserStatisticsFunctions

# Or directly with pytest
pytest tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions -v
```

### Run with Coverage
```bash
pytest tests/test_query_vup_user_data_refactored.py --cov=vups_server.query.query_vup_user_data_refactored --cov-report=html
```

## Test Coverage

The tests provide comprehensive coverage of:
- ✅ All 40+ functions in the module
- ✅ Success and failure scenarios
- ✅ Parameter validation
- ✅ Return value verification
- ✅ Service method call verification
- ✅ Error handling and exception propagation
- ✅ Edge cases and boundary conditions

## Mock Verification

Each test verifies:
1. **Correct Service Method Calls**: Ensures the right service method is called
2. **Parameter Passing**: Verifies parameters are passed correctly to services
3. **Return Value Handling**: Confirms return values are processed properly
4. **Exception Propagation**: Tests that service exceptions bubble up correctly

## Example Test Pattern

```python
@pytest.mark.asyncio
@patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
async def test_function_name(self, mock_service):
    """Test description."""
    # Setup
    mock_service.method_name.return_value = expected_result
    
    # Execute
    result = await query_module.function_under_test("param1", "param2")
    
    # Assert
    assert result == expected_result
    mock_service.method_name.assert_called_once_with("param1", "param2")
```

This pattern ensures complete isolation of the module under test while verifying all interactions with its dependencies.
