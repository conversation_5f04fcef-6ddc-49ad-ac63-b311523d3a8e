#!/usr/bin/env python3
"""
Test runner script for query_vup_user_data_refactored tests.
This script demonstrates how to run the comprehensive mock tests.
"""

import sys
import subprocess
from pathlib import Path

def run_tests():
    """Run the comprehensive mock tests for query_vup_user_data_refactored module."""
    
    # Get the test file path
    test_file = Path(__file__).parent / "test_query_vup_user_data_refactored.py"
    
    if not test_file.exists():
        print(f"Error: Test file not found at {test_file}")
        return 1
    
    print("=" * 80)
    print("Running comprehensive mock tests for query_vup_user_data_refactored module")
    print("=" * 80)
    
    # Run pytest with verbose output
    cmd = [
        sys.executable, "-m", "pytest",
        str(test_file),
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--durations=10",  # Show 10 slowest tests
        "--cov=vups_server.query.query_vup_user_data_refactored",  # Coverage report
        "--cov-report=term-missing",  # Show missing lines
    ]
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except FileNotFoundError:
        print("Error: pytest not found. Please install pytest:")
        print("pip install pytest pytest-asyncio pytest-cov")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def run_specific_test_class(class_name):
    """Run tests for a specific test class."""
    
    test_file = Path(__file__).parent / "test_query_vup_user_data_refactored.py"
    
    print(f"Running tests for class: {class_name}")
    print("-" * 50)
    
    cmd = [
        sys.executable, "-m", "pytest",
        f"{test_file}::{class_name}",
        "-v",
        "--tb=short",
    ]
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main function to handle command line arguments."""
    
    if len(sys.argv) > 1:
        # Run specific test class
        class_name = sys.argv[1]
        return run_specific_test_class(class_name)
    else:
        # Run all tests
        return run_tests()


if __name__ == "__main__":
    exit_code = main()
    
    if exit_code == 0:
        print("\n" + "=" * 80)
        print("✅ All tests completed successfully!")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ Some tests failed. Check the output above for details.")
        print("=" * 80)
    
    sys.exit(exit_code)
