import os
from vups import logger
from vups.algos.tools.video_dump import video_source_downloader
from datetime import datetime
import tabulate as tb
from bilibili_api import video, Credential, search
from mcp.server.fastmcp import FastMCP

from vups.algos.tools.asr import AliParaformerASR, BCutASR
from vups.algos.tools.subtitle_task_manager import task_manager
from vups_server.base.cookie_manager import get_cookie_field
from vups_server.base.response_schema import create_success_response, create_error_response
from vups.utils import get_user_mid, get_user_fullname
from vups_server.query.query_vup_user_data import * # noqa: F403
from vups_server.query.user_statistics_queries import user_stats_service
from vups_server.query.content_queries import content_service
from vups_server.query.analytics_queries import analytics_service
from vups_server.query.specialized_queries import specialized_service

# USING ENV
SESSDATA = os.getenv('sessdata')
BILI_JCT = os.getenv('bili_jct')
BUVID3 = os.getenv('buvid3')

if not all([SESSDATA, BILI_JCT, BUVID3]):
    SESSDATA = get_cookie_field("user", "SESSDATA")
    BILI_JCT = get_cookie_field("user", "bili_jct")
    BUVID3 = get_cookie_field("user", "buvid3")
credential = Credential(sessdata=SESSDATA, bili_jct=BILI_JCT, buvid3=BUVID3)

mcp = FastMCP("vups-mcp")

# --------------------------------------- BASIC ---------------------------------------
@mcp.tool("get_user_current_follower", description="获取up主当前粉丝数，需提供up主名称")
async def get_user_current_follower(char: str) -> dict:
    """
    char: up主名称
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        follower_count = await query_now_user_follower_num_by_mid(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": follower_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current follower count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current followers for Vup {vtuber}: {e}")
        raise (
            create_error_response("Failed to retrieve follower count", 500)
        )

@mcp.tool("get_user_current_stats", description="获取up主当前完整统计数据，包括粉丝数、大航海数等，需提供up主名称")
async def get_user_current_stats(char: str) -> dict:
    """
    Get comprehensive current statistics for a VTuber including followers, dahanghai, etc.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with current statistics data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        current_stats = await user_stats_service.get_current_stat_by_uid(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": current_stats,
                "timestamp": datetime.now().isoformat()
            },
            message="Current statistics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current stats for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve current statistics", 500)
        )

@mcp.tool("get_user_current_dahanghai", description="获取up主当前大航海数，需提供up主名称")
async def get_user_current_dahanghai(char: str) -> dict:
    """
    Get current dahanghai (membership) count for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with current dahanghai count or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        dahanghai_count = await user_stats_service.get_current_dahanghai_count(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": dahanghai_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current dahanghai count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current dahanghai for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve dahanghai count", 500)
        )

@mcp.tool("get_user_follower_change", description="获取up主粉丝变化数，可指定回溯天数，需提供up主名称")
async def get_user_follower_change(char: str, recent_days: int = 1) -> dict:
    """
    Get follower count change for a VTuber over specified days.

    Args:
        char: VTuber name or identifier
        recent_days: Number of days to look back (default: 1)

    Returns:
        dict: Success response with follower change data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        follower_change = await user_stats_service.get_follower_change(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": follower_change,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Follower change over {recent_days} days retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get follower change for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve follower change", 500)
        )

@mcp.tool("get_user_dahanghai_change", description="获取up主大航海变化数，可指定回溯天数，需提供up主名称")
async def get_user_dahanghai_change(char: str, recent_days: int = 1) -> dict:
    """
    Get dahanghai count change for a VTuber over specified days.

    Args:
        char: VTuber name or identifier
        recent_days: Number of days to look back (default: 1)

    Returns:
        dict: Success response with dahanghai change data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        dahanghai_change = await user_stats_service.get_dahanghai_change(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": dahanghai_change,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Dahanghai change over {recent_days} days retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dahanghai change for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve dahanghai change", 500)
        )

# --------------------------------------- USER STATISTICS ---------------------------------------

@mcp.tool("get_user_follower_growth_rate", description="计算up主粉丝增长率，可指定计算时间窗口，需提供up主名称")
async def get_user_follower_growth_rate(char: str, recent_days: int = 90) -> dict:
    """
    Calculate follower growth rate for a VTuber over specified time window.

    Args:
        char: VTuber name or identifier
        recent_days: Time window in days for calculation (default: 90)

    Returns:
        dict: Success response with formatted growth rate string or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        growth_rate = await user_stats_service.calculate_follower_growth_rate(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": growth_rate,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Follower growth rate over {recent_days} days calculated successfully"
        )
    except Exception as e:
        logger.error(f"Failed to calculate follower growth rate for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to calculate follower growth rate", 500)
        )

@mcp.tool("get_user_dahanghai_growth_rate", description="计算up主大航海增长率，可指定计算时间窗口，需提供up主名称")
async def get_user_dahanghai_growth_rate(char: str, recent_days: int = 90) -> dict:
    """
    Calculate dahanghai growth rate for a VTuber over specified time window.

    Args:
        char: VTuber name or identifier
        recent_days: Time window in days for calculation (default: 90)

    Returns:
        dict: Success response with formatted growth rate string or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        growth_rate = await user_stats_service.calculate_dahanghai_growth_rate(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": growth_rate,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Dahanghai growth rate over {recent_days} days calculated successfully"
        )
    except Exception as e:
        logger.error(f"Failed to calculate dahanghai growth rate for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to calculate dahanghai growth rate", 500)
        )

@mcp.tool("get_user_stats_by_period", description="获取up主指定时间段内的所有统计数据，需提供up主名称和时间范围")
async def get_user_stats_by_period(char: str, start_time: str, end_time: str) -> dict:
    """
    Get all statistics for a VTuber within a specific time period.

    Args:
        char: VTuber name or identifier
        start_time: Start time string (YYYY-MM-DD format)
        end_time: End time string (YYYY-MM-DD format)

    Returns:
        dict: Success response with formatted statistics list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        stats_data = await user_stats_service.get_user_stats_by_time_range(mid, start_time, end_time)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": stats_data,
                "start_time": start_time,
                "end_time": end_time,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Statistics from {start_time} to {end_time} retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get period stats for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve period statistics", 500)
        )

@mcp.tool("get_user_stats_recent", description="获取up主最近指定天数的所有统计数据，需提供up主名称")
async def get_user_stats_recent(char: str, recent_days: int = -1) -> dict:
    """
    Get all statistics for a VTuber for recent days.

    Args:
        char: VTuber name or identifier
        recent_days: Number of recent days (-1 for all data, default: -1)

    Returns:
        dict: Success response with formatted statistics list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        stats_data = await user_stats_service.get_user_stats_recent_days(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": stats_data,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Recent statistics for {recent_days} days retrieved successfully" if recent_days != -1 else "All historical statistics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get recent stats for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve recent statistics", 500)
        )

@mcp.tool("get_user_follower_history", description="获取up主最近指定天数的粉丝数历史，需提供up主名称")
async def get_user_follower_history(char: str, recent_days: int = -1) -> dict:
    """
    Get follower number history for a VTuber for recent days.

    Args:
        char: VTuber name or identifier
        recent_days: Number of recent days (-1 for all data, default: -1)

    Returns:
        dict: Success response with follower history data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        follower_history = await user_stats_service.get_follower_history(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": follower_history,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Follower history for {recent_days} days retrieved successfully" if recent_days != -1 else "Complete follower history retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get follower history for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve follower history", 500)
        )

@mcp.tool("get_user_dahanghai_history", description="获取up主最近指定天数的大航海数历史，需提供up主名称")
async def get_user_dahanghai_history(char: str, recent_days: int = -1) -> dict:
    """
    Get dahanghai number history for a VTuber for recent days.

    Args:
        char: VTuber name or identifier
        recent_days: Number of recent days (-1 for all data, default: -1)

    Returns:
        dict: Success response with dahanghai history data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        dahanghai_history = await user_stats_service.get_dahanghai_history(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": dahanghai_history,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Dahanghai history for {recent_days} days retrieved successfully" if recent_days != -1 else "Complete dahanghai history retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dahanghai history for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve dahanghai history", 500)
        )

# --------------------------------------- CONTENT TOOLS ---------------------------------------

@mcp.tool("get_user_basic_info", description="获取up主基本信息，需提供up主名称")
async def get_user_basic_info(char: str) -> dict:
    """
    Get basic information for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with user information record or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        user_info = await content_service.get_user_info_by_uid(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": user_info,
                "timestamp": datetime.now().isoformat()
            },
            message="User basic information retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get basic info for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve user basic information", 500)
        )

@mcp.tool("get_user_all_dynamics", description="获取up主所有动态，包含热度计算，需提供up主名称")
async def get_user_all_dynamics(char: str) -> dict:
    """
    Get all dynamics for a VTuber with heat calculation.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with dynamics list including heat values or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        dynamics = await content_service.get_user_dynamics(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": dynamics,
                "timestamp": datetime.now().isoformat()
            },
            message="All user dynamics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dynamics for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve user dynamics", 500)
        )

@mcp.tool("get_user_all_videos", description="获取up主所有视频，包含热度计算，需提供up主名称")
async def get_user_all_videos(char: str) -> dict:
    """
    Get all videos for a VTuber with heat calculation.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with videos list including heat values or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        videos = await content_service.get_user_videos(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": videos,
                "timestamp": datetime.now().isoformat()
            },
            message="All user videos retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get videos for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve user videos", 500)
        )

@mcp.tool("get_user_latest_video", description="获取up主最新视频，需提供up主名称")
async def get_user_latest_video(char: str) -> dict:
    """
    Get the most recent video for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with latest video information or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        latest_video = await content_service.get_latest_video(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": latest_video,
                "timestamp": datetime.now().isoformat()
            },
            message="Latest video retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get latest video for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve latest video", 500)
        )

@mcp.tool("get_user_latest_dynamic", description="获取up主最新动态，需提供up主名称")
async def get_user_latest_dynamic(char: str) -> dict:
    """
    Get the most recent dynamic for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with latest dynamic information or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        latest_dynamic = await content_service.get_latest_dynamic(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": latest_dynamic,
                "timestamp": datetime.now().isoformat()
            },
            message="Latest dynamic retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get latest dynamic for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve latest dynamic", 500)
        )

@mcp.tool("get_user_video_comments", description="获取up主所有视频评论，需提供up主名称")
async def get_user_video_comments(char: str) -> dict:
    """
    Get all video comments for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with video comments list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        video_comments = await content_service.get_video_comments(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": video_comments,
                "timestamp": datetime.now().isoformat()
            },
            message="Video comments retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get video comments for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve video comments", 500)
        )

@mcp.tool("get_user_dynamics_comments", description="获取up主所有动态评论，需提供up主名称")
async def get_user_dynamics_comments(char: str) -> dict:
    """
    Get all dynamics comments for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with dynamics comments list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        dynamics_comments = await content_service.get_dynamics_comments(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": dynamics_comments,
                "timestamp": datetime.now().isoformat()
            },
            message="Dynamics comments retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dynamics comments for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve dynamics comments", 500)
        )

# --------------------------------------- VIDEO ---------------------------------------
asr = AliParaformerASR(
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    streaming_mode=True,
)

# asr = BCutASR()

@mcp.tool("search_video", description="搜索bilibili视频")
async def search_video(keyword: str, page: int = 1, page_size: int = 20) -> str:
    """
    keyword: 搜索关键词
    page: 页码，默认1
    page_size: 每页数量，默认20
    """
    search_result = await search.search_by_type(keyword, search_type=search.SearchObjectType.VIDEO, page=page, page_size=page_size)

    table_data = []
    headers = ["发布日期", "标题", "UP主", "时长", "播放量", "点赞数", "类别", "bvid"]

    for video in search_result["result"]:
        pubdate = datetime.fromtimestamp(video["pubdate"]).strftime("%Y/%m/%d")

        title_link = f"[{video['title']}]({video['arcurl']})"

        table_data.append([
            pubdate,
            title_link,
            video["author"],
            video["duration"],
            video["play"],
            video["like"],
            video["typename"],
            video["bvid"]
        ])

    return tb.tabulate(table_data, headers=headers, tablefmt="pipe")

@mcp.tool("get_video_subtitle", description="获取bilibili视频的字幕，需提供视频bvid")
async def get_video_subtitle(bvid: str) -> dict:
    """
    bvid: bvid
    """
    return await video_source_downloader.download_subtitle(bvid=bvid, output_filename="tests/vups/data/test_subtitle.txt")

@mcp.tool("get_video_info", description="获取bilibili视频信息，需提供视频BV号")
async def get_video_info(bvid: str) -> dict:
    """
    bvid: 视频BV号
    """
    v = video.Video(bvid=bvid, credential=credential)
    info = await v.get_info()
    return info

@mcp.tool("get_media_subtitle", description="获取媒体文件的AI中文字幕，需提供媒体文件URL")
async def get_media_subtitle(url: str) -> dict:
    """
    url: 媒体文件URL
    """
    asr_data = await asr.transcribe([url])
    return asr_data
# --------------------------------------- VIDEO ---------------------------------------






# @mcp.tool("start_video_subtitle_task", description="开始bilibili视频字幕生成任务（异步处理，适合长视频）")
# async def start_video_subtitle_task(bvid: str, output_filename: str = None) -> dict:
#     """
#     bvid: 视频BV号
#     output_filename: 输出文件名（可选）
#     返回: {"task_id": "任务ID", "status": "pending", "message": "任务已创建"}
#     """
#     task_id = task_manager.create_task(bvid, output_filename)
#     return {
#         "task_id": task_id,
#         "status": "pending",
#         "message": f"Subtitle generation task created for video {bvid}. Use get_subtitle_task_status to check progress."
#     }

# @mcp.tool("get_subtitle_task_status", description="查询字幕生成任务状态和进度")
# async def get_subtitle_task_status(task_id: str) -> dict:
#     """
#     task_id: 任务ID
#     返回: 任务状态、进度百分比、当前步骤等信息
#     """
#     progress = task_manager.get_task_status(task_id)
#     if not progress:
#         return {"error": "Task not found", "task_id": task_id}

#     return {
#         "task_id": task_id,
#         "status": progress.status.value,
#         "progress_percent": progress.progress_percent,
#         "current_step": progress.current_step,
#         "estimated_remaining": progress.estimated_remaining,
#         "error_message": progress.error_message,
#         "created_at": progress.created_at.isoformat() if progress.created_at else None,
#         "updated_at": progress.updated_at.isoformat() if progress.updated_at else None
#     }

# @mcp.tool("get_subtitle_task_result", description="获取已完成的字幕生成任务结果")
# async def get_subtitle_task_result(task_id: str) -> dict:
#     """
#     task_id: 任务ID
#     返回: 任务结果（仅当任务完成时）
#     """
#     progress = task_manager.get_task_status(task_id)
#     if not progress:
#         return {"error": "Task not found", "task_id": task_id}

#     if progress.status.value != "completed":
#         return {
#             "error": f"Task not completed. Current status: {progress.status.value}",
#             "task_id": task_id,
#             "status": progress.status.value
#         }

#     return {
#         "task_id": task_id,
#         "status": "completed",
#         "result": progress.result,
#         "completed_at": progress.updated_at.isoformat() if progress.updated_at else None
#     }

# @mcp.tool("cancel_subtitle_task", description="取消正在进行的字幕生成任务")
# async def cancel_subtitle_task(task_id: str) -> dict:
#     """
#     task_id: 任务ID
#     返回: 取消操作结果
#     """
#     success = task_manager.cancel_task(task_id)
#     if success:
#         return {"task_id": task_id, "status": "cancelled", "message": "Task cancelled successfully"}
#     else:
#         return {"task_id": task_id, "error": "Failed to cancel task or task not found"}

# --------------------------------------- ANALYTICS TOOLS ---------------------------------------

@mcp.tool("get_user_top_comments", description="获取up主热门评论，可指定返回数量，需提供up主名称")
async def get_user_top_comments(char: str, limit: int = 10) -> dict:
    """
    Get top comments for a VTuber with enhanced caching.

    Args:
        char: VTuber name or identifier
        limit: Number of comments to return (default: 10)

    Returns:
        dict: Success response with top comments list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        top_comments = await analytics_service.get_top_comments(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": top_comments,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Top {limit} comments retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get top comments for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve top comments", 500)
        )

@mcp.tool("get_user_top_comment_users", description="获取up主热门评论用户，可指定返回数量，需提供up主名称")
async def get_user_top_comment_users(char: str, limit: int = 10) -> dict:
    """
    Get top comment users for a VTuber with enhanced caching.

    Args:
        char: VTuber name or identifier
        limit: Number of users to return (default: 10)

    Returns:
        dict: Success response with top comment users list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        top_comment_users = await analytics_service.get_top_comment_users(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": top_comment_users,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Top {limit} comment users retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get top comment users for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve top comment users", 500)
        )

@mcp.tool("get_user_top_videos", description="获取up主热门视频，基于热度评分，可指定返回数量，需提供up主名称")
async def get_user_top_videos(char: str, limit: int = 10) -> dict:
    """
    Get top videos for a VTuber based on heat score.

    Args:
        char: VTuber name or identifier
        limit: Number of videos to return (default: 10)

    Returns:
        dict: Success response with top videos list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        top_videos = await analytics_service.get_top_videos(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": top_videos,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Top {limit} videos retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get top videos for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve top videos", 500)
        )

@mcp.tool("get_user_top_dynamics", description="获取up主热门动态，基于热度评分，可指定返回数量，需提供up主名称")
async def get_user_top_dynamics(char: str, limit: int = 10) -> dict:
    """
    Get top dynamics for a VTuber based on heat score.

    Args:
        char: VTuber name or identifier
        limit: Number of dynamics to return (default: 10)

    Returns:
        dict: Success response with top dynamics list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        top_dynamics = await analytics_service.get_top_dynamics(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": top_dynamics,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Top {limit} dynamics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get top dynamics for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve top dynamics", 500)
        )

@mcp.tool("get_user_recent_top_videos", description="获取up主最近热门视频，基于热度评分，可指定返回数量，需提供up主名称")
async def get_user_recent_top_videos(char: str, limit: int = 10) -> dict:
    """
    Get recent top videos for a VTuber based on heat score.

    Args:
        char: VTuber name or identifier
        limit: Number of videos to return (default: 10)

    Returns:
        dict: Success response with recent top videos list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        recent_top_videos = await content_service.get_recent_top_videos(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": recent_top_videos,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Recent top {limit} videos retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get recent top videos for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve recent top videos", 500)
        )

@mcp.tool("get_user_comments_for_wordcloud", description="获取用于词云生成的评论，可指定处理数量，需提供up主名称")
async def get_user_comments_for_wordcloud(char: str, limit: int = 1000) -> dict:
    """
    Get comments for word cloud generation.

    Args:
        char: VTuber name or identifier
        limit: Number of comments to process (default: 1000)

    Returns:
        dict: Success response with comments content list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        comments = await content_service.get_comments_for_wordcloud(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": comments,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Comments for wordcloud ({limit} limit) retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get comments for wordcloud for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve comments for wordcloud", 500)
        )

@mcp.tool("get_user_recent_relationships", description="获取up主最近关系数据，来自AI生成数据，可指定返回数量，需提供up主名称")
async def get_user_recent_relationships(char: str, limit: int = 10) -> dict:
    """
    Get recent relationships from AI-generated data.

    Args:
        char: VTuber name or identifier
        limit: Number of relationships to return (default: 10)

    Returns:
        dict: Success response with recent relationships list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        relationships = await analytics_service.get_recent_relationships(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": relationships,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Recent {limit} relationships retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get recent relationships for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve recent relationships", 500)
        )

@mcp.tool("get_user_recent_comments_sentiment", description="获取up主最近评论情感值，可指定分析天数，需提供up主名称")
async def get_user_recent_comments_sentiment(char: str, recent_days: int = 30) -> dict:
    """
    Get recent comments sentiment value for a VTuber.

    Args:
        char: VTuber name or identifier
        recent_days: Number of recent days to analyze (default: 30)

    Returns:
        dict: Success response with average sentiment value or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        sentiment_value = await analytics_service.get_recent_comments_sentiment(mid, recent_days)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": sentiment_value,
                "recent_days": recent_days,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Recent comments sentiment for {recent_days} days retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get comments sentiment for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve comments sentiment", 500)
        )

@mcp.tool("get_user_fans_medal_rank", description="获取up主最新粉丝勋章排行，需提供up主名称")
async def get_user_fans_medal_rank(char: str) -> dict:
    """
    Get latest fans medal rank data for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with fans medal rank data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        medal_rank = await analytics_service.get_fans_medal_rank(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": medal_rank,
                "timestamp": datetime.now().isoformat()
            },
            message="Latest fans medal rank retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get fans medal rank for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve fans medal rank", 500)
        )

# --------------------------------------- SPECIALIZED TOOLS ---------------------------------------

@mcp.tool("get_user_tieba_data", description="获取up主贴吧数据，包含统计和详细数据，需提供up主名称")
async def get_user_tieba_data(char: str) -> dict:
    """
    Get comprehensive tieba data for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with tieba statistics and data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        tieba_data = await specialized_service.get_tieba_data(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": tieba_data,
                "timestamp": datetime.now().isoformat()
            },
            message="Tieba data retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get tieba data for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve tieba data", 500)
        )

@mcp.tool("get_user_dahanghai_list", description="获取up主最新大航海列表，需提供up主名称")
async def get_user_dahanghai_list(char: str) -> dict:
    """
    Get latest dahanghai list for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with dahanghai list data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        dahanghai_list = await specialized_service.get_dahanghai_list(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": dahanghai_list,
                "timestamp": datetime.now().isoformat()
            },
            message="Latest dahanghai list retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dahanghai list for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve dahanghai list", 500)
        )

@mcp.tool("get_user_followers_list", description="获取up主粉丝列表，需提供up主名称")
async def get_user_followers_list(char: str) -> dict:
    """
    Get followers list for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with followers list data or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        followers_list = await specialized_service.get_followers_list(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": followers_list,
                "timestamp": datetime.now().isoformat()
            },
            message="Followers list retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get followers list for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve followers list", 500)
        )

@mcp.tool("get_user_followers_review_data", description="获取up主粉丝审核数据和比率，需提供up主名称")
async def get_user_followers_review_data(char: str) -> dict:
    """
    Get followers review data and rate for a VTuber.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with followers review data and rate or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        review_data = await specialized_service.get_followers_review_data(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": review_data,
                "timestamp": datetime.now().isoformat()
            },
            message="Followers review data retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get followers review data for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve followers review data", 500)
        )

@mcp.tool("generate_user_comment_wordcloud", description="生成up主评论词云图片，可指定处理评论数量，需提供up主名称")
async def generate_user_comment_wordcloud(char: str, limit: int = 1000) -> dict:
    """
    Generate word cloud from user comments.

    Args:
        char: VTuber name or identifier
        limit: Number of comments to process (default: 1000)

    Returns:
        dict: Success response with generated word cloud image path or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        wordcloud_path = await specialized_service.generate_comment_wordcloud(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": wordcloud_path,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Word cloud generated successfully from {limit} comments"
        )
    except Exception as e:
        logger.error(f"Failed to generate wordcloud for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to generate comment wordcloud", 500)
        )

# --------------------------------------- AI-GENERATED DATA TOOLS ---------------------------------------

@mcp.tool("get_user_tieba_summaries", description="获取up主贴吧摘要，来自AI生成数据，可指定返回数量，需提供up主名称")
async def get_user_tieba_summaries(char: str, limit: int = 5) -> dict:
    """
    Get tieba summaries from AI-generated data.

    Args:
        char: VTuber name or identifier
        limit: Number of summaries to return (default: 5)

    Returns:
        dict: Success response with tieba summaries list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        summaries = await analytics_service.get_tieba_summaries(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": summaries,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Tieba summaries ({limit}) retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get tieba summaries for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve tieba summaries", 500)
        )

@mcp.tool("get_user_rise_reasons", description="获取up主上升原因，来自AI生成数据，可指定返回数量，需提供up主名称")
async def get_user_rise_reasons(char: str, limit: int = 5) -> dict:
    """
    Get rise reasons from AI-generated data.

    Args:
        char: VTuber name or identifier
        limit: Number of reasons to return (default: 5)

    Returns:
        dict: Success response with rise reasons list or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        rise_reasons = await analytics_service.get_rise_reasons(mid, limit)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": rise_reasons,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            },
            message=f"Rise reasons ({limit}) retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get rise reasons for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve rise reasons", 500)
        )

@mcp.tool("get_video_ai_conclusion", description="获取视频AI结论，需提供视频BVID")
async def get_video_ai_conclusion(bvid: str) -> dict:
    """
    Get AI conclusion for a specific video.

    Args:
        bvid: Video BVID identifier

    Returns:
        dict: Success response with AI conclusion text or error response
    """
    try:
        ai_conclusion = await analytics_service.get_video_ai_conclusion(bvid)
        return create_success_response(
            data={
                "bvid": bvid,
                "result": ai_conclusion,
                "timestamp": datetime.now().isoformat()
            },
            message="Video AI conclusion retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get AI conclusion for video {bvid}: {e}")
        raise (
            create_error_response("Failed to retrieve video AI conclusion", 500)
        )

# --------------------------------------- COMPREHENSIVE INFO TOOLS ---------------------------------------

@mcp.tool("get_user_comprehensive_info", description="获取up主最近活动，整合多个数据源，需提供up主名称")
async def get_user_comprehensive_info(char: str) -> dict:
    """
    Get comprehensive user information combining multiple data sources.

    Args:
        char: VTuber name or identifier

    Returns:
        dict: Success response with comprehensive user information or error response
    """
    try:
        mid = get_user_mid(char)
        vtuber = get_user_fullname(mid)
        comprehensive_info = await specialized_service.get_comprehensive_user_info(mid)
        return create_success_response(
            data={
                "vtuber": vtuber,
                "mid": mid,
                "result": comprehensive_info,
                "timestamp": datetime.now().isoformat()
            },
            message="Comprehensive user information retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get comprehensive info for VTuber {char}: {e}")
        raise (
            create_error_response("Failed to retrieve comprehensive user information", 500)
        )


mcp.run()
